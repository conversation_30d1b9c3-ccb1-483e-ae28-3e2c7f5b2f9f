// Frida脚本 - 专门用于提取UDP解密密钥
// 使用方法: frida -U -f <应用包名> -l key_extract.js --no-pause

console.log("[*] 开始UDP解密密钥提取...");

// 工具函数：格式化十六进制数据
var formatHexData = function(buffer, length) {
    if (!buffer || length <= 0) return "";
    
    var result = "";
    var bytes = new Uint8Array(buffer);
    
    for (var i = 0; i < Math.min(length, bytes.length); i += 16) {
        result += ("0000" + i.toString(16)).slice(-4) + ": ";
        
        var hex = "";
        var ascii = "";
        for (var j = 0; j < 16; j++) {
            if (i + j < Math.min(length, bytes.length)) {
                var byte = bytes[i + j];
                hex += ("0" + byte.toString(16)).slice(-2) + " ";
                ascii += (byte >= 32 && byte <= 126) ? String.fromCharCode(byte) : ".";
            } else {
                hex += "   ";
                ascii += " ";
            }
        }
        
        result += hex + " |" + ascii + "|\n";
    }
    
    return result;
};

// 获取模块基址
var baseAddr = Module.getBaseAddress("amgddd.dylib");
console.log("[+] amgddd.dylib 基址: " + baseAddr);

// ==================== 关键函数Hook ====================

// 1. Hook GetKey函数 - 密钥生成
var getKeyAddr = baseAddr.add(0x1bf40);
console.log("[+] Hook GetKey函数: " + getKeyAddr);

Interceptor.attach(getKeyAddr, {
    onEnter: function(args) {
        console.log("\n" + "=".repeat(60));
        console.log("🔑 [GetKey] 密钥生成函数被调用!");
        
        this.inputData = args[0];
        this.inputLen = args[1].toInt32();
        this.outputKey = args[2];
        
        console.log("输入数据长度: " + this.inputLen);
        
        if (this.inputData && this.inputLen > 0) {
            try {
                var inputBytes = this.inputData.readByteArray(this.inputLen);
                console.log("🔍 输入数据:");
                console.log(formatHexData(inputBytes, this.inputLen));
            } catch (e) {
                console.log("❌ 读取输入数据失败: " + e.message);
            }
        }
    },
    onLeave: function(retval) {
        console.log("📤 GetKey返回值: " + retval);
        
        if (this.outputKey) {
            console.log("🔑 尝试提取生成的密钥:");
            
            // 尝试不同的密钥长度
            [8, 16, 24, 32, 48, 64].forEach(function(keyLen) {
                try {
                    var keyBytes = this.outputKey.readByteArray(keyLen);
                    var keyArray = new Uint8Array(keyBytes);
                    
                    // 检查是否为有效密钥（非全零且有变化）
                    var nonZero = false;
                    var hasVariation = false;
                    var firstByte = keyArray[0];
                    
                    for (var i = 0; i < keyArray.length; i++) {
                        if (keyArray[i] !== 0) nonZero = true;
                        if (keyArray[i] !== firstByte) hasVariation = true;
                    }
                    
                    if (nonZero && hasVariation) {
                        console.log("✅ 发现有效密钥 (长度: " + keyLen + " 字节):");
                        console.log(formatHexData(keyBytes, keyLen));
                        
                        // 转换为十六进制字符串
                        var hexKey = "";
                        for (var i = 0; i < keyArray.length; i++) {
                            hexKey += ("0" + keyArray[i].toString(16)).slice(-2);
                        }
                        console.log("🔑 密钥 (Hex String): " + hexKey);
                        
                        // 尝试解析为ASCII
                        var asciiKey = "";
                        var isPrintable = true;
                        for (var i = 0; i < keyArray.length; i++) {
                            if (keyArray[i] >= 32 && keyArray[i] <= 126) {
                                asciiKey += String.fromCharCode(keyArray[i]);
                            } else {
                                isPrintable = false;
                                break;
                            }
                        }
                        if (isPrintable && asciiKey.length > 0) {
                            console.log("🔑 密钥 (ASCII): " + asciiKey);
                        }
                    }
                } catch (e) {
                    // 继续尝试其他长度
                }
            }.bind(this));
        }
        console.log("=".repeat(60) + "\n");
    }
});

// 2. Hook sub_100106函数 - 主解密函数
var decryptAddr = baseAddr.add(0x12a1c);
console.log("[+] Hook 解密函数: " + decryptAddr);

Interceptor.attach(decryptAddr, {
    onEnter: function(args) {
        console.log("\n" + "=".repeat(60));
        console.log("🔓 [DECRYPT] 解密函数被调用!");
        
        this.inputData = args[0];
        this.inputLen = args[1].toInt32();
        this.keyData = args[2];
        this.keyLen = args[3].toInt32();
        this.outputData = args[4];
        this.outputLen = args[5];
        
        console.log("📊 参数信息:");
        console.log("  - 输入数据长度: " + this.inputLen);
        console.log("  - 密钥长度: " + this.keyLen);
        
        // 打印加密数据
        if (this.inputData && this.inputLen > 0) {
            try {
                var inputBytes = this.inputData.readByteArray(this.inputLen);
                console.log("🔒 加密数据:");
                console.log(formatHexData(inputBytes, Math.min(this.inputLen, 128))); // 限制显示长度
            } catch (e) {
                console.log("❌ 读取加密数据失败: " + e.message);
            }
        }
        
        // 打印密钥
        if (this.keyData && this.keyLen > 0) {
            try {
                var keyBytes = this.keyData.readByteArray(this.keyLen);
                console.log("🔑 *** 解密密钥 ***:");
                console.log(formatHexData(keyBytes, this.keyLen));
                
                // 转换为十六进制字符串
                var keyArray = new Uint8Array(keyBytes);
                var hexKey = "";
                for (var i = 0; i < keyArray.length; i++) {
                    hexKey += ("0" + keyArray[i].toString(16)).slice(-2);
                }
                console.log("🔑 密钥 (Hex): " + hexKey);
                
                // 尝试ASCII解析
                var asciiKey = "";
                var isPrintable = true;
                for (var i = 0; i < keyArray.length; i++) {
                    if (keyArray[i] >= 32 && keyArray[i] <= 126) {
                        asciiKey += String.fromCharCode(keyArray[i]);
                    } else {
                        isPrintable = false;
                        break;
                    }
                }
                if (isPrintable && asciiKey.length > 0) {
                    console.log("🔑 密钥 (ASCII): " + asciiKey);
                }
            } catch (e) {
                console.log("❌ 读取密钥失败: " + e.message);
            }
        }
    },
    onLeave: function(retval) {
        console.log("📤 解密函数返回值: " + retval);
        
        // 打印解密结果
        if (this.outputData && this.outputLen) {
            try {
                var outputLenValue = this.outputLen.readInt();
                if (outputLenValue > 0) {
                    var outputBytes = this.outputData.readByteArray(outputLenValue);
                    console.log("🔓 解密结果 (长度: " + outputLenValue + "):");
                    console.log(formatHexData(outputBytes, Math.min(outputLenValue, 128)));
                    
                    // 尝试解析为字符串
                    try {
                        var outputStr = this.outputData.readUtf8String(outputLenValue);
                        if (outputStr && outputStr.length > 0) {
                            console.log("📝 解密结果 (String): " + outputStr);
                        }
                    } catch (e) {
                        // 忽略字符串解析错误
                    }
                }
            } catch (e) {
                console.log("❌ 读取解密结果失败: " + e.message);
            }
        }
        console.log("=".repeat(60) + "\n");
    }
});

// 3. Hook HexToByte函数 - 监控十六进制转换
var hexToByteAddr = baseAddr.add(0x17a58);
console.log("[+] Hook HexToByte函数: " + hexToByteAddr);

Interceptor.attach(hexToByteAddr, {
    onEnter: function(args) {
        if (args[0]) {
            try {
                var hexStr = args[0].readUtf8String();
                if (hexStr && hexStr.length > 20) { // 只关注较长的十六进制字符串
                    console.log("🔄 [HEX2BYTE] 转换: " + hexStr.substring(0, 80) + (hexStr.length > 80 ? "..." : ""));
                }
            } catch (e) {
                // 忽略错误
            }
        }
    }
});

// 4. 搜索可能的密钥常量
console.log("[*] 搜索可能的密钥常量...");

var moduleInfo = Process.getModuleByName("amgddd.dylib");
var keyPatterns = [
    "key", "Key", "KEY",
    "password", "Password", "PASSWORD", 
    "secret", "Secret", "SECRET",
    "0123456789abcdef",
    "abcdef0123456789"
];

keyPatterns.forEach(function(pattern) {
    try {
        Memory.scan(moduleInfo.base, moduleInfo.size, pattern, {
            onMatch: function(address, size) {
                console.log("🔍 [KEY_SEARCH] 找到模式 '" + pattern + "' 在: " + address);
                try {
                    var context = address.readUtf8String(64);
                    console.log("    上下文: " + context.replace(/\n/g, "\\n"));
                } catch (e) {
                    // 忽略读取错误
                }
            },
            onComplete: function() {
                // 搜索完成
            }
        });
    } catch (e) {
        // 忽略搜索错误
    }
});

console.log("[✅] 密钥提取脚本加载完成!");
console.log("[📡] 等待UDP通信触发解密过程...");
