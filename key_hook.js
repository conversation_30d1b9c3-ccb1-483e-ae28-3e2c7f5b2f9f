// Frida脚本 - UDP解密密钥提取
// 使用方法: frida -U -f <应用包名> -l key_hook.js --no-pause

console.log("[*] 开始UDP解密密钥提取...");

// 工具函数：格式化十六进制数据
var formatHexData = function(buffer, length) {
    if (!buffer || length <= 0) return "";
    
    var result = "";
    var bytes = new Uint8Array(buffer);
    
    for (var i = 0; i < Math.min(length, bytes.length); i += 16) {
        result += ("0000" + i.toString(16)).slice(-4) + ": ";
        
        var hex = "";
        var ascii = "";
        for (var j = 0; j < 16; j++) {
            if (i + j < Math.min(length, bytes.length)) {
                var byte = bytes[i + j];
                hex += ("0" + byte.toString(16)).slice(-2) + " ";
                ascii += (byte >= 32 && byte <= 126) ? String.fromCharCode(byte) : ".";
            } else {
                hex += "   ";
                ascii += " ";
            }
        }
        
        result += hex + " |" + ascii + "|\n";
    }
    
    return result;
};

// 动态查找目标模块
var targetModule = null;
var modules = Process.enumerateModules();

console.log("[*] 查找目标模块...");
for (var i = 0; i < modules.length; i++) {
    var module = modules[i];
    
    // 查找包含我们需要的函数的模块
    if (module.name.toLowerCase().indexOf('amg') !== -1 || 
        module.name.toLowerCase().indexOf('ddd') !== -1 ||
        module.path.toLowerCase().indexOf('amg') !== -1) {
        targetModule = module;
        console.log("[+] 找到目标模块: " + module.name + " - " + module.base);
        break;
    }
}

// 如果没找到特定模块，使用主执行文件
if (!targetModule) {
    targetModule = modules[0]; // 通常是主执行文件
    console.log("[+] 使用主模块: " + targetModule.name + " - " + targetModule.base);
}

var baseAddr = targetModule.base;

// ==================== 关键函数Hook ====================

// 1. Hook GetKey函数 - 密钥生成
console.log("[*] 设置GetKey函数Hook...");
try {
    var getKeyAddr = baseAddr.add(0x1bf40);
    console.log("[+] GetKey函数地址: " + getKeyAddr);

    Interceptor.attach(getKeyAddr, {
        onEnter: function(args) {
            console.log("\n" + "=".repeat(60));
            console.log("🔑 [GetKey] 密钥生成函数被调用!");
            
            this.inputData = args[0];
            this.inputLen = args[1].toInt32();
            this.outputKey = args[2];
            
            console.log("输入数据长度: " + this.inputLen);
            
            if (this.inputData && this.inputLen > 0) {
                try {
                    var inputBytes = this.inputData.readByteArray(this.inputLen);
                    console.log("🔍 输入数据:");
                    console.log(formatHexData(inputBytes, this.inputLen));
                } catch (e) {
                    console.log("❌ 读取输入数据失败: " + e.message);
                }
            }
        },
        onLeave: function(retval) {
            console.log("📤 GetKey返回值: " + retval);
            
            if (this.outputKey) {
                console.log("🔑 尝试提取生成的密钥:");
                
                // 尝试不同的密钥长度
                [8, 16, 24, 32, 48, 64].forEach(function(keyLen) {
                    try {
                        var keyBytes = this.outputKey.readByteArray(keyLen);
                        var keyArray = new Uint8Array(keyBytes);
                        
                        // 检查是否为有效密钥（非全零且有变化）
                        var nonZero = false;
                        var hasVariation = false;
                        var firstByte = keyArray[0];
                        
                        for (var i = 0; i < keyArray.length; i++) {
                            if (keyArray[i] !== 0) nonZero = true;
                            if (keyArray[i] !== firstByte) hasVariation = true;
                        }
                        
                        if (nonZero && hasVariation) {
                            console.log("✅ 发现有效密钥 (长度: " + keyLen + " 字节):");
                            console.log(formatHexData(keyBytes, keyLen));
                            
                            // 转换为十六进制字符串
                            var hexKey = "";
                            for (var i = 0; i < keyArray.length; i++) {
                                hexKey += ("0" + keyArray[i].toString(16)).slice(-2);
                            }
                            console.log("🔑 密钥 (Hex String): " + hexKey);
                            
                            // 尝试解析为ASCII
                            var asciiKey = "";
                            var isPrintable = true;
                            for (var i = 0; i < keyArray.length; i++) {
                                if (keyArray[i] >= 32 && keyArray[i] <= 126) {
                                    asciiKey += String.fromCharCode(keyArray[i]);
                                } else {
                                    isPrintable = false;
                                    break;
                                }
                            }
                            if (isPrintable && asciiKey.length > 0) {
                                console.log("🔑 密钥 (ASCII): " + asciiKey);
                            }
                        }
                    } catch (e) {
                        // 继续尝试其他长度
                    }
                }.bind(this));
            }
            console.log("=".repeat(60) + "\n");
        }
    });
    console.log("[✅] GetKey函数Hook设置成功");
} catch (e) {
    console.log("[-] GetKey函数Hook失败: " + e.message);
}

// 2. Hook sub_100106函数 - 主解密函数
console.log("[*] 设置解密函数Hook...");
try {
    var decryptAddr = baseAddr.add(0x12a1c);
    console.log("[+] 解密函数地址: " + decryptAddr);

    Interceptor.attach(decryptAddr, {
        onEnter: function(args) {
            console.log("\n" + "=".repeat(60));
            console.log("🔓 [DECRYPT] 解密函数被调用!");
            
            this.inputData = args[0];
            this.inputLen = args[1].toInt32();
            this.keyData = args[2];
            this.keyLen = args[3].toInt32();
            this.outputData = args[4];
            this.outputLen = args[5];
            
            console.log("📊 参数信息:");
            console.log("  - 输入数据长度: " + this.inputLen);
            console.log("  - 密钥长度: " + this.keyLen);
            
            // 打印加密数据
            if (this.inputData && this.inputLen > 0) {
                try {
                    var inputBytes = this.inputData.readByteArray(this.inputLen);
                    console.log("🔒 加密数据:");
                    console.log(formatHexData(inputBytes, Math.min(this.inputLen, 128))); // 限制显示长度
                } catch (e) {
                    console.log("❌ 读取加密数据失败: " + e.message);
                }
            }
            
            // 打印密钥
            if (this.keyData && this.keyLen > 0) {
                try {
                    var keyBytes = this.keyData.readByteArray(this.keyLen);
                    console.log("🔑 *** 解密密钥 ***:");
                    console.log(formatHexData(keyBytes, this.keyLen));
                    
                    // 转换为十六进制字符串
                    var keyArray = new Uint8Array(keyBytes);
                    var hexKey = "";
                    for (var i = 0; i < keyArray.length; i++) {
                        hexKey += ("0" + keyArray[i].toString(16)).slice(-2);
                    }
                    console.log("🔑 密钥 (Hex): " + hexKey);
                    
                    // 尝试ASCII解析
                    var asciiKey = "";
                    var isPrintable = true;
                    for (var i = 0; i < keyArray.length; i++) {
                        if (keyArray[i] >= 32 && keyArray[i] <= 126) {
                            asciiKey += String.fromCharCode(keyArray[i]);
                        } else {
                            isPrintable = false;
                            break;
                        }
                    }
                    if (isPrintable && asciiKey.length > 0) {
                        console.log("🔑 密钥 (ASCII): " + asciiKey);
                    }
                } catch (e) {
                    console.log("❌ 读取密钥失败: " + e.message);
                }
            }
        },
        onLeave: function(retval) {
            console.log("📤 解密函数返回值: " + retval);
            
            // 打印解密结果
            if (this.outputData && this.outputLen) {
                try {
                    var outputLenValue = this.outputLen.readInt();
                    if (outputLenValue > 0) {
                        var outputBytes = this.outputData.readByteArray(outputLenValue);
                        console.log("🔓 解密结果 (长度: " + outputLenValue + "):");
                        console.log(formatHexData(outputBytes, Math.min(outputLenValue, 128)));
                        
                        // 尝试解析为字符串
                        try {
                            var outputStr = this.outputData.readUtf8String(outputLenValue);
                            if (outputStr && outputStr.length > 0) {
                                console.log("📝 解密结果 (String): " + outputStr);
                            }
                        } catch (e) {
                            // 忽略字符串解析错误
                        }
                    }
                } catch (e) {
                    console.log("❌ 读取解密结果失败: " + e.message);
                }
            }
            console.log("=".repeat(60) + "\n");
        }
    });
    console.log("[✅] 解密函数Hook设置成功");
} catch (e) {
    console.log("[-] 解密函数Hook失败: " + e.message);
}

// 3. Hook UDP接收函数
console.log("[*] 设置UDP监控...");
var recvfromPtr = Module.findExportByName(null, "recvfrom");
if (recvfromPtr) {
    Interceptor.attach(recvfromPtr, {
        onLeave: function(retval) {
            var received_len = retval.toInt32();
            if (received_len > 0) {
                var socktype = Socket.type(this.sockfd);
                if (socktype === 'udp' || socktype === 'udp6') {
                    console.log("\n🌐 [UDP] 接收到数据 (" + received_len + " 字节)");
                }
            }
        }
    });
    console.log("[✅] UDP监控设置成功");
}

console.log("\n[🚀] 密钥提取脚本加载完成!");
console.log("[📡] 等待UDP通信触发解密过程...");
console.log("[💡] 提示: 触发应用的网络通信来捕获密钥信息");
