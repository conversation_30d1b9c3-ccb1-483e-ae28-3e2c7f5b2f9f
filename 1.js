// Frida iOS UDP 监控脚本
// 监控UDP发送和接收数据，并打印堆栈信息

console.log("[*] 开始监控 UDP 通信...");

// 工具函数：格式化十六进制数据
var formatHexData = function(buffer, length) {
    if (!buffer || length <= 0) return "";

    var result = "";
    var bytes = new Uint8Array(buffer);

    for (var i = 0; i < Math.min(length, bytes.length); i += 16) {
        // 地址
        result += ("0000" + i.toString(16)).slice(-4) + ": ";

        // 十六进制
        var hex = "";
        var ascii = "";
        for (var j = 0; j < 16; j++) {
            if (i + j < Math.min(length, bytes.length)) {
                var byte = bytes[i + j];
                hex += ("0" + byte.toString(16)).slice(-2) + " ";
                ascii += (byte >= 32 && byte <= 126) ? String.fromCharCode(byte) : ".";
            } else {
                hex += "   ";
                ascii += " ";
            }
        }

        result += hex + " |" + ascii + "|\n";
    }

    return result;
};

// 工具函数：获取调用堆栈
var printCallStack = function() {
    try {
        console.log("调用堆栈:");
        console.log(Thread.backtrace(this.context, Backtracer.FUZZY)
            .map(addr => {
                const m = Process.findModuleByAddress(addr);
                if (m) {
                    const offset = addr.sub(m.base);
                    return `${m.name}!0x${addr.toString(16)} -> IDA偏移: 0x${offset.toString(16)}`;
                }
                return `0x${addr.toString(16)}`;
            })
            .join('\n')
        );
        console.log("[+] ---------------------------------------------------------------");
    } catch (e) {
        console.log("堆栈获取失败: " + e.message);
        console.log("[+] ---------------------------------------------------------------");
    }
};

// 监控 sendto 系统调用
var sendtoPtr = Module.findExportByName(null, "sendto");
if (sendtoPtr) {
    Interceptor.attach(sendtoPtr, {
        onEnter: function(args) {
            this.sockfd = args[0].toInt32();
            this.buf = args[1];
            this.len = args[2].toInt32();
            this.flags = args[3].toInt32();
            this.dest_addr = args[4];
            this.addrlen = args[5].toInt32();

            // 检查是否是UDP socket
            var socktype = Socket.type(this.sockfd);
            if (socktype === 'udp' || socktype === 'udp6') {
                console.log("\n[UDP SEND] ==========================================");
                console.log("Socket FD: " + this.sockfd);
                console.log("Data Length: " + this.len + " bytes");
                console.log("Flags: 0x" + this.flags.toString(16));

                if (this.len > 0 && this.buf) {
                    try {
                        var data = this.buf.readByteArray(this.len);
                        console.log("发送数据 (Hex):");
                        console.log(formatHexData(data, this.len));

                        // 尝试解析为字符串
                        try {
                            var str = this.buf.readUtf8String(this.len);
                            if (str && str.length > 0) {
                                console.log("发送数据 (String): " + str);
                            }
                        } catch (e) {
                            // 忽略字符串解析错误
                        }
                    } catch (e) {
                        console.log("读取发送数据失败: " + e.message);
                    }
                }

                printCallStack.call(this);
            }
        },
        onLeave: function(retval) {
            if (this.sockfd && Socket.type(this.sockfd) &&
                (Socket.type(this.sockfd) === 'udp' || Socket.type(this.sockfd) === 'udp6')) {
                console.log("发送结果: " + retval.toInt32() + " bytes");
                console.log("================================================\n");
            }
        }
    });
    console.log("[+] 已hook sendto函数");
} else {
    console.log("[-] 未找到sendto函数");
}

// 监控 recvfrom 系统调用
var recvfromPtr = Module.findExportByName(null, "recvfrom");
if (recvfromPtr) {
    Interceptor.attach(recvfromPtr, {
        onEnter: function(args) {
            this.sockfd = args[0].toInt32();
            this.buf = args[1];
            this.len = args[2].toInt32();
            this.flags = args[3].toInt32();
            this.src_addr = args[4];
            this.addrlen = args[5];
        },
        onLeave: function(retval) {
            var received_len = retval.toInt32();

            if (received_len > 0) {
                // 检查是否是UDP socket
                var socktype = Socket.type(this.sockfd);
                if (socktype === 'udp' || socktype === 'udp6') {
                    console.log("\n[UDP RECV] ==========================================");
                    console.log("Socket FD: " + this.sockfd);
                    console.log("Received Length: " + received_len + " bytes");
                    console.log("Flags: 0x" + this.flags.toString(16));

                    if (this.buf) {
                        try {
                            var data = this.buf.readByteArray(received_len);
                            console.log("接收数据 (Hex):");
                            console.log(formatHexData(data, received_len));

                            // 尝试解析为字符串
                            try {
                                var str = this.buf.readUtf8String(received_len);
                                if (str && str.length > 0) {
                                    console.log("接收数据 (String): " + str);
                                }
                            } catch (e) {
                                // 忽略字符串解析错误
                            }
                        } catch (e) {
                            console.log("读取接收数据失败: " + e.message);
                        }
                    }

                    printCallStack.call(this);
                    console.log("================================================\n");
                }
            }
        }
    });
    console.log("[+] 已hook recvfrom函数");
} else {
    console.log("[-] 未找到recvfrom函数");
}

// 监控 send 系统调用 (可能用于已连接的UDP socket)
var sendPtr = Module.findExportByName(null, "send");
if (sendPtr) {
    Interceptor.attach(sendPtr, {
        onEnter: function(args) {
            this.sockfd = args[0].toInt32();
            this.buf = args[1];
            this.len = args[2].toInt32();
            this.flags = args[3].toInt32();

            var socktype = Socket.type(this.sockfd);
            if (socktype === 'udp' || socktype === 'udp6') {
                console.log("\n[UDP SEND (connected)] ==========================");
                console.log("Socket FD: " + this.sockfd);
                console.log("Data Length: " + this.len + " bytes");
                console.log("Flags: 0x" + this.flags.toString(16));

                if (this.len > 0 && this.buf) {
                    try {
                        var data = this.buf.readByteArray(this.len);
                        console.log("发送数据 (Hex):");
                        console.log(formatHexData(data, this.len));

                        try {
                            var str = this.buf.readUtf8String(this.len);
                            if (str && str.length > 0) {
                                console.log("发送数据 (String): " + str);
                            }
                        } catch (e) {
                            // 忽略字符串解析错误
                        }
                    } catch (e) {
                        console.log("读取发送数据失败: " + e.message);
                    }
                }

                printCallStack.call(this);
            }
        },
        onLeave: function(retval) {
            if (this.sockfd && Socket.type(this.sockfd) &&
                (Socket.type(this.sockfd) === 'udp' || Socket.type(this.sockfd) === 'udp6')) {
                console.log("发送结果: " + retval.toInt32() + " bytes");
                console.log("================================================\n");
            }
        }
    });
    console.log("[+] 已hook send函数");
} else {
    console.log("[-] 未找到send函数");
}

// 监控 recv 系统调用
var recvPtr = Module.findExportByName(null, "recv");
if (recvPtr) {
    Interceptor.attach(recvPtr, {
        onEnter: function(args) {
            this.sockfd = args[0].toInt32();
            this.buf = args[1];
            this.len = args[2].toInt32();
            this.flags = args[3].toInt32();
        },
        onLeave: function(retval) {
            var received_len = retval.toInt32();

            if (received_len > 0) {
                var socktype = Socket.type(this.sockfd);
                if (socktype === 'udp' || socktype === 'udp6') {
                    console.log("\n[UDP RECV (connected)] ==========================");
                    console.log("Socket FD: " + this.sockfd);
                    console.log("Received Length: " + received_len + " bytes");
                    console.log("Flags: 0x" + this.flags.toString(16));

                    if (this.buf) {
                        try {
                            var data = this.buf.readByteArray(received_len);
                            console.log("接收数据 (Hex):");
                            console.log(formatHexData(data, received_len));

                            try {
                                var str = this.buf.readUtf8String(received_len);
                                if (str && str.length > 0) {
                                    console.log("接收数据 (String): " + str);
                                }
                            } catch (e) {
                                // 忽略字符串解析错误
                            }
                        } catch (e) {
                            console.log("读取接收数据失败: " + e.message);
                        }
                    }

                    printCallStack.call(this);
                    console.log("================================================\n");
                }
            }
        }
    });
    console.log("[+] 已hook recv函数");
} else {
    console.log("[-] 未找到recv函数");
}

// ==================== 解密分析相关Hook ====================

// 动态查找目标模块
var targetModule = null;
var modules = Process.enumerateModules();

console.log("[*] 查找目标模块...");
for (var i = 0; i < modules.length; i++) {
    var module = modules[i];
    console.log("模块: " + module.name + " - " + module.base);

    // 查找包含我们需要的函数的模块
    if (module.name.toLowerCase().indexOf('amg') !== -1 ||
        module.name.toLowerCase().indexOf('ddd') !== -1 ||
        module.path.toLowerCase().indexOf('amg') !== -1) {
        targetModule = module;
        console.log("[+] 找到目标模块: " + module.name);
        break;
    }
}

// 如果没找到特定模块，使用主执行文件
if (!targetModule) {
    targetModule = modules[0]; // 通常是主执行文件
    console.log("[+] 使用主模块: " + targetModule.name);
}

var baseAddr = targetModule.base;
console.log("[+] 目标模块基址: " + baseAddr);

// Hook GetKey函数 - 获取密钥生成过程
try {
    var getKeyAddr = baseAddr.add(0x1bf40);
    console.log("[+] GetKey函数地址: " + getKeyAddr);

    Interceptor.attach(getKeyAddr, {
    onEnter: function(args) {
        console.log("\n[GETKEY] ==========================================");
        console.log("GetKey函数被调用!");

        // 参数1: const unsigned __int8 *a1 (输入数据)
        this.inputData = args[0];
        this.inputLen = args[1].toInt32();
        this.outputKey = args[2];

        console.log("输入数据长度: " + this.inputLen);

        if (this.inputData && this.inputLen > 0) {
            try {
                var inputBytes = this.inputData.readByteArray(this.inputLen);
                console.log("输入数据 (Hex):");
                console.log(formatHexData(inputBytes, this.inputLen));

                // 尝试解析为字符串
                try {
                    var inputStr = this.inputData.readUtf8String(this.inputLen);
                    if (inputStr && inputStr.length > 0) {
                        console.log("输入数据 (String): " + inputStr);
                    }
                } catch (e) {
                    // 忽略字符串解析错误
                }
            } catch (e) {
                console.log("读取输入数据失败: " + e.message);
            }
        }

        printCallStack.call(this);
    },
    onLeave: function(retval) {
        console.log("GetKey返回值: " + retval);

        // 尝试读取生成的密钥
        if (this.outputKey) {
            try {
                // 假设密钥长度为32字节，可以根据实际情况调整
                for (var keyLen = 16; keyLen <= 64; keyLen += 16) {
                    try {
                        var keyBytes = this.outputKey.readByteArray(keyLen);
                        console.log("生成的密钥 (长度" + keyLen + "):");
                        console.log(formatHexData(keyBytes, keyLen));

                        // 检查是否为有效密钥（非全零）
                        var keyArray = new Uint8Array(keyBytes);
                        var nonZero = false;
                        for (var i = 0; i < keyArray.length; i++) {
                            if (keyArray[i] !== 0) {
                                nonZero = true;
                                break;
                            }
                        }
                        if (nonZero) {
                            console.log("*** 可能的有效密钥 (长度" + keyLen + ") ***");
                        }
                    } catch (e) {
                        // 继续尝试其他长度
                    }
                }
            } catch (e) {
                console.log("读取密钥失败: " + e.message);
            }
        }
        console.log("================================================\n");
    }
    });
} catch (e) {
    console.log("[-] GetKey函数Hook失败: " + e.message);
}

// Hook sub_100106函数 - 主要的加密/解密函数
try {
    var sub100106Addr = baseAddr.add(0x12a1c);
    console.log("[+] sub_100106函数地址: " + sub100106Addr);

    Interceptor.attach(sub100106Addr, {
    onEnter: function(args) {
        console.log("\n[DECRYPT] ==========================================");
        console.log("sub_100106解密函数被调用!");

        // 参数解析
        this.inputData = args[0];      // const unsigned __int8 *a1 (输入数据)
        this.inputLen = args[1].toInt32();  // int a2 (输入长度)
        this.keyData = args[2];        // const unsigned __int8 *a3 (密钥数据)
        this.keyLen = args[3].toInt32();    // int a4 (密钥长度)
        this.outputData = args[4];     // unsigned __int8 *a5 (输出数据)
        this.outputLen = args[5];      // int *a6 (输出长度指针)

        console.log("输入数据长度: " + this.inputLen);
        console.log("密钥长度: " + this.keyLen);

        // 打印输入数据
        if (this.inputData && this.inputLen > 0) {
            try {
                var inputBytes = this.inputData.readByteArray(this.inputLen);
                console.log("输入数据 (加密数据):");
                console.log(formatHexData(inputBytes, this.inputLen));
            } catch (e) {
                console.log("读取输入数据失败: " + e.message);
            }
        }

        // 打印密钥数据
        if (this.keyData && this.keyLen > 0) {
            try {
                var keyBytes = this.keyData.readByteArray(this.keyLen);
                console.log("*** 解密密钥 ***:");
                console.log(formatHexData(keyBytes, this.keyLen));

                // 尝试解析密钥为字符串
                try {
                    var keyStr = this.keyData.readUtf8String(this.keyLen);
                    if (keyStr && keyStr.length > 0) {
                        console.log("密钥 (String): " + keyStr);
                    }
                } catch (e) {
                    // 忽略字符串解析错误
                }
            } catch (e) {
                console.log("读取密钥失败: " + e.message);
            }
        }

        printCallStack.call(this);
    },
    onLeave: function(retval) {
        console.log("sub_100106返回值: " + retval);

        // 打印解密后的输出数据
        if (this.outputData && this.outputLen) {
            try {
                var outputLenValue = this.outputLen.readInt();
                if (outputLenValue > 0) {
                    var outputBytes = this.outputData.readByteArray(outputLenValue);
                    console.log("解密后数据 (长度: " + outputLenValue + "):");
                    console.log(formatHexData(outputBytes, outputLenValue));

                    // 尝试解析为字符串
                    try {
                        var outputStr = this.outputData.readUtf8String(outputLenValue);
                        if (outputStr && outputStr.length > 0) {
                            console.log("*** 解密后数据 (String): " + outputStr + " ***");
                        }
                    } catch (e) {
                        // 忽略字符串解析错误
                    }
                }
            } catch (e) {
                console.log("读取输出数据失败: " + e.message);
            }
        }
        console.log("================================================\n");
    }
    });
} catch (e) {
    console.log("[-] sub_100106函数Hook失败: " + e.message);
}

// Hook HexToByte函数 - 十六进制转字节
var hexToByteAddr = baseAddr.add(0x17a58);
console.log("[+] HexToByte函数地址: " + hexToByteAddr);

Interceptor.attach(hexToByteAddr, {
    onEnter: function(args) {
        this.hexStr = args[0];

        if (this.hexStr) {
            try {
                var hexString = this.hexStr.readUtf8String();
                if (hexString && hexString.length > 10) { // 只打印较长的十六进制字符串
                    console.log("\n[HEX2BYTE] 十六进制转换: " + hexString.substring(0, 100) + (hexString.length > 100 ? "..." : ""));
                }
            } catch (e) {
                // 忽略错误
            }
        }
    },
    onLeave: function(retval) {
        // 可以在这里打印转换结果
    }
});

// Hook ByteToHex函数 - 字节转十六进制
var byteToHexAddr = baseAddr.add(0x1516c);
console.log("[+] ByteToHex函数地址: " + byteToHexAddr);

Interceptor.attach(byteToHexAddr, {
    onEnter: function(args) {
        this.byteData = args[0];
        // 这个函数可能有长度参数，但从反编译看不太清楚，先简单处理
    },
    onLeave: function(retval) {
        if (retval && !retval.isNull()) {
            try {
                var hexResult = retval.readUtf8String();
                if (hexResult && hexResult.length > 10) {
                    console.log("\n[BYTE2HEX] 字节转十六进制结果: " + hexResult.substring(0, 100) + (hexResult.length > 100 ? "..." : ""));
                }
            } catch (e) {
                // 忽略错误
            }
        }
    }
});

// Hook 可能的XOR解密函数 - 通过模式匹配查找
function hookPossibleXORFunctions() {
    // 使用已经找到的baseAddr

    // 搜索可能的XOR操作模式
    var ranges = Process.enumerateRanges('r-x').filter(function(range) {
        return range.file && range.file.path && range.file.path.indexOf(targetModule.name) !== -1;
    });

    ranges.forEach(function(range) {
        try {
            // 搜索XOR指令模式 (EOR指令在ARM64中)
            Memory.scan(range.base, range.size, "?? ?? ?? ?? ?? ?? ?? ??", {
                onMatch: function(address, size) {
                    // 这里可以添加更具体的XOR模式检测
                },
                onComplete: function() {
                    // 扫描完成
                }
            });
        } catch (e) {
            // 忽略扫描错误
        }
    });
}

// Hook 数据处理相关的关键函数
var dataProcessAddr = baseAddr.add(0x195b60);
console.log("[+] 数据处理函数地址: " + dataProcessAddr);

Interceptor.attach(dataProcessAddr, {
    onEnter: function(args) {
        console.log("\n[DATA_PROCESS] ==========================================");
        console.log("数据处理函数被调用!");

        // 这个函数可能接收NSString参数
        this.nsString = args[0];

        if (this.nsString && !this.nsString.isNull()) {
            try {
                // 尝试读取NSString内容
                var ObjC = require('frida-objc-bridge');
                if (ObjC.available) {
                    var nsStr = new ObjC.Object(this.nsString);
                    var strContent = nsStr.toString();
                    console.log("NSString内容: " + strContent);
                }
            } catch (e) {
                console.log("读取NSString失败: " + e.message);
            }
        }

        printCallStack.call(this);
    },
    onLeave: function(retval) {
        console.log("数据处理函数返回值: " + retval);
        console.log("================================================\n");
    }
});

// Hook 可能的字符串处理函数
function hookStringFunctions() {
    // 使用已经找到的baseAddr

    // Hook strstr函数 (如果存在)
    try {
        var strstrAddr = baseAddr.add(0x111b0); // __ZL6strstrUa9enable_ifILb1EEPKcS0_
        Interceptor.attach(strstrAddr, {
            onEnter: function(args) {
                try {
                    var str1 = args[0].readUtf8String();
                    var str2 = args[1].readUtf8String();
                    if (str1 && str2) {
                        console.log("\n[STRSTR] 字符串搜索: '" + str1 + "' 中查找 '" + str2 + "'");
                    }
                } catch (e) {
                    // 忽略错误
                }
            }
        });
        console.log("[+] strstr函数Hook设置完成");
    } catch (e) {
        console.log("[-] strstr函数Hook失败: " + e.message);
    }
}

// 添加内存搜索功能，查找可能的密钥常量
function searchForKeys() {
    console.log("[*] 开始搜索可能的密钥常量...");

    // 使用已经找到的targetModule
    var moduleSize = targetModule.size;

    // 搜索一些常见的密钥模式
    var keyPatterns = [
        "0123456789abcdef",
        "abcdef0123456789",
        "1234567890abcdef",
        "key",
        "password",
        "secret"
    ];

    keyPatterns.forEach(function(pattern) {
        try {
            Memory.scan(baseAddr, moduleSize, pattern, {
                onMatch: function(address, size) {
                    console.log("[KEY_SEARCH] 找到可能的密钥模式 '" + pattern + "' 在地址: " + address);
                    try {
                        var context = address.readUtf8String(32);
                        console.log("上下文: " + context);
                    } catch (e) {
                        // 忽略读取错误
                    }
                },
                onComplete: function() {
                    // 搜索完成
                }
            });
        } catch (e) {
            // 忽略搜索错误
        }
    });
}

// 执行额外的Hook设置
hookStringFunctions();
searchForKeys();

console.log("[*] UDP 监控脚本加载完成!");
console.log("[*] 解密分析Hook已设置!");
console.log("[*] 等待UDP通信...");