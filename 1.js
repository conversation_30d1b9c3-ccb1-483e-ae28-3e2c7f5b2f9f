// Frida iOS UDP 监控脚本
// 监控UDP发送和接收数据，并打印堆栈信息

console.log("[*] 开始监控 UDP 通信...");

// 工具函数：格式化十六进制数据
var formatHexData = function(buffer, length) {
    if (!buffer || length <= 0) return "";

    var result = "";
    var bytes = new Uint8Array(buffer);

    for (var i = 0; i < Math.min(length, bytes.length); i += 16) {
        // 地址
        result += ("0000" + i.toString(16)).slice(-4) + ": ";

        // 十六进制
        var hex = "";
        var ascii = "";
        for (var j = 0; j < 16; j++) {
            if (i + j < Math.min(length, bytes.length)) {
                var byte = bytes[i + j];
                hex += ("0" + byte.toString(16)).slice(-2) + " ";
                ascii += (byte >= 32 && byte <= 126) ? String.fromCharCode(byte) : ".";
            } else {
                hex += "   ";
                ascii += " ";
            }
        }

        result += hex + " |" + ascii + "|\n";
    }

    return result;
};

// 工具函数：获取调用堆栈
var printCallStack = function() {
    try {
        console.log("调用堆栈:");
        console.log(Thread.backtrace(this.context, Backtracer.FUZZY)
            .map(addr => {
                const m = Process.findModuleByAddress(addr);
                if (m) {
                    const offset = addr.sub(m.base);
                    return `${m.name}!0x${addr.toString(16)} -> IDA偏移: 0x${offset.toString(16)}`;
                }
                return `0x${addr.toString(16)}`;
            })
            .join('\n')
        );
        console.log("[+] ---------------------------------------------------------------");
    } catch (e) {
        console.log("堆栈获取失败: " + e.message);
        console.log("[+] ---------------------------------------------------------------");
    }
};

// 监控 sendto 系统调用
var sendtoPtr = Module.findExportByName(null, "sendto");
if (sendtoPtr) {
    Interceptor.attach(sendtoPtr, {
        onEnter: function(args) {
            this.sockfd = args[0].toInt32();
            this.buf = args[1];
            this.len = args[2].toInt32();
            this.flags = args[3].toInt32();
            this.dest_addr = args[4];
            this.addrlen = args[5].toInt32();

            // 检查是否是UDP socket
            var socktype = Socket.type(this.sockfd);
            if (socktype === 'udp' || socktype === 'udp6') {
                console.log("\n[UDP SEND] ==========================================");
                console.log("Socket FD: " + this.sockfd);
                console.log("Data Length: " + this.len + " bytes");
                console.log("Flags: 0x" + this.flags.toString(16));

                if (this.len > 0 && this.buf) {
                    try {
                        var data = this.buf.readByteArray(this.len);
                        console.log("发送数据 (Hex):");
                        console.log(formatHexData(data, this.len));

                        // 尝试解析为字符串
                        try {
                            var str = this.buf.readUtf8String(this.len);
                            if (str && str.length > 0) {
                                console.log("发送数据 (String): " + str);
                            }
                        } catch (e) {
                            // 忽略字符串解析错误
                        }
                    } catch (e) {
                        console.log("读取发送数据失败: " + e.message);
                    }
                }

                printCallStack.call(this);
            }
        },
        onLeave: function(retval) {
            if (this.sockfd && Socket.type(this.sockfd) &&
                (Socket.type(this.sockfd) === 'udp' || Socket.type(this.sockfd) === 'udp6')) {
                console.log("发送结果: " + retval.toInt32() + " bytes");
                console.log("================================================\n");
            }
        }
    });
    console.log("[+] 已hook sendto函数");
} else {
    console.log("[-] 未找到sendto函数");
}

// 监控 recvfrom 系统调用
var recvfromPtr = Module.findExportByName(null, "recvfrom");
if (recvfromPtr) {
    Interceptor.attach(recvfromPtr, {
        onEnter: function(args) {
            this.sockfd = args[0].toInt32();
            this.buf = args[1];
            this.len = args[2].toInt32();
            this.flags = args[3].toInt32();
            this.src_addr = args[4];
            this.addrlen = args[5];
        },
        onLeave: function(retval) {
            var received_len = retval.toInt32();

            if (received_len > 0) {
                // 检查是否是UDP socket
                var socktype = Socket.type(this.sockfd);
                if (socktype === 'udp' || socktype === 'udp6') {
                    console.log("\n[UDP RECV] ==========================================");
                    console.log("Socket FD: " + this.sockfd);
                    console.log("Received Length: " + received_len + " bytes");
                    console.log("Flags: 0x" + this.flags.toString(16));

                    if (this.buf) {
                        try {
                            var data = this.buf.readByteArray(received_len);
                            console.log("接收数据 (Hex):");
                            console.log(formatHexData(data, received_len));

                            // 尝试解析为字符串
                            try {
                                var str = this.buf.readUtf8String(received_len);
                                if (str && str.length > 0) {
                                    console.log("接收数据 (String): " + str);
                                }
                            } catch (e) {
                                // 忽略字符串解析错误
                            }
                        } catch (e) {
                            console.log("读取接收数据失败: " + e.message);
                        }
                    }

                    printCallStack.call(this);
                    console.log("================================================\n");
                }
            }
        }
    });
    console.log("[+] 已hook recvfrom函数");
} else {
    console.log("[-] 未找到recvfrom函数");
}

// 监控 send 系统调用 (可能用于已连接的UDP socket)
var sendPtr = Module.findExportByName(null, "send");
if (sendPtr) {
    Interceptor.attach(sendPtr, {
        onEnter: function(args) {
            this.sockfd = args[0].toInt32();
            this.buf = args[1];
            this.len = args[2].toInt32();
            this.flags = args[3].toInt32();

            var socktype = Socket.type(this.sockfd);
            if (socktype === 'udp' || socktype === 'udp6') {
                console.log("\n[UDP SEND (connected)] ==========================");
                console.log("Socket FD: " + this.sockfd);
                console.log("Data Length: " + this.len + " bytes");
                console.log("Flags: 0x" + this.flags.toString(16));

                if (this.len > 0 && this.buf) {
                    try {
                        var data = this.buf.readByteArray(this.len);
                        console.log("发送数据 (Hex):");
                        console.log(formatHexData(data, this.len));

                        try {
                            var str = this.buf.readUtf8String(this.len);
                            if (str && str.length > 0) {
                                console.log("发送数据 (String): " + str);
                            }
                        } catch (e) {
                            // 忽略字符串解析错误
                        }
                    } catch (e) {
                        console.log("读取发送数据失败: " + e.message);
                    }
                }

                printCallStack.call(this);
            }
        },
        onLeave: function(retval) {
            if (this.sockfd && Socket.type(this.sockfd) &&
                (Socket.type(this.sockfd) === 'udp' || Socket.type(this.sockfd) === 'udp6')) {
                console.log("发送结果: " + retval.toInt32() + " bytes");
                console.log("================================================\n");
            }
        }
    });
    console.log("[+] 已hook send函数");
} else {
    console.log("[-] 未找到send函数");
}

// 监控 recv 系统调用
var recvPtr = Module.findExportByName(null, "recv");
if (recvPtr) {
    Interceptor.attach(recvPtr, {
        onEnter: function(args) {
            this.sockfd = args[0].toInt32();
            this.buf = args[1];
            this.len = args[2].toInt32();
            this.flags = args[3].toInt32();
        },
        onLeave: function(retval) {
            var received_len = retval.toInt32();

            if (received_len > 0) {
                var socktype = Socket.type(this.sockfd);
                if (socktype === 'udp' || socktype === 'udp6') {
                    console.log("\n[UDP RECV (connected)] ==========================");
                    console.log("Socket FD: " + this.sockfd);
                    console.log("Received Length: " + received_len + " bytes");
                    console.log("Flags: 0x" + this.flags.toString(16));

                    if (this.buf) {
                        try {
                            var data = this.buf.readByteArray(received_len);
                            console.log("接收数据 (Hex):");
                            console.log(formatHexData(data, received_len));

                            try {
                                var str = this.buf.readUtf8String(received_len);
                                if (str && str.length > 0) {
                                    console.log("接收数据 (String): " + str);
                                }
                            } catch (e) {
                                // 忽略字符串解析错误
                            }
                        } catch (e) {
                            console.log("读取接收数据失败: " + e.message);
                        }
                    }

                    try {
                        console.log("调用堆栈:");
                        console.log(getCallStack.call(this));
                    } catch (e) {
                        console.log("堆栈打印失败: " + e.message);
                    }
                    console.log("================================================\n");
                }
            }
        }
    });
    console.log("[+] 已hook recv函数");
} else {
    console.log("[-] 未找到recv函数");
}

console.log("[*] UDP 监控脚本加载完成!");
console.log("[*] 等待UDP通信...");